<script setup lang="ts">
import Banner from './banner/index.vue'
import Search from './search/index.vue'
import Option from './option/index.vue'

const levels = ['三级甲等', '三级乙等', '二级甲等', '二级乙等', '一级甲等', '一级乙等']
const regions = ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区']
</script>

<template>
  <Banner />
  <Search />
  <el-row :gutter="20">
    <el-col :span="20" class="option">
      <span>医院</span>
      <Option name="等级" :options="levels" />
      <Option name="地区" :options="regions" />
    </el-col>
    <el-col :span="4">
      456
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.option {
  span {
    color: #7f7f7f;
    display: inline-block;
    margin: 10px 0;
  }
}
</style>